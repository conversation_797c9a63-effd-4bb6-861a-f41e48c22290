# 依赖升级指南

本文档记录了项目依赖升级的过程和注意事项，以便团队成员了解升级过程中的变化和需要注意的问题。

## Fabric.js 6.7.0 升级

### 变更概述

Fabric.js 从之前的版本升级到 6.7.0 版本，API 发生了较大变化：

1. 类名重命名：

   - `fabric.Object` 重命名为 `FabricObject`
   - `fabric.Text` 重命名为 `FabricText`
   - `fabric.Image` 重命名为 `FabricImage`

2. 导入方式变化：

   - 之前：`import { fabric } from "fabric";` 然后使用 `fabric.Rect`, `fabric.Circle` 等
   - 现在：直接导入具体类 `import { Rect, Circle, Path } from "fabric";`

3. 属性访问方式变化：
   - 部分属性可能需要通过 `get()` 方法访问，例如：
     - `textTarget.stroke` 可能需要改为 `textTarget.get('stroke')`
     - `textTarget.backgroundColor` 可能需要改为 `textTarget.get('backgroundColor')`

### 升级步骤

1. 更新 package.json 中的 fabric 版本到 6.7.0
2. 修改 ShapeFactory.ts 中的类型导入和使用方式
3. 修改 ElementManager.ts 中的属性访问方式，添加 fallback 机制
4. 修改 fabric-utils.ts 中的 TextboxWithPadding 类，添加 get 方法

### 遇到的问题及解决方案

1. **类型定义问题**

   - 问题：`fabric.Path`, `fabric.Group`, `fabric.Polygon` 等类型在新版本中不存在
   - 解决：直接从 fabric 导入具体类 `import { Path, Group, Polygon } from "fabric";`

2. **属性访问问题**

   - 问题：直接访问 `textTarget.stroke`, `textTarget.backgroundColor` 等属性报错
   - 解决：添加 fallback 机制，优先使用直接访问，然后尝试 `get()` 方法

   ```typescript
   strokeColor: textTarget.stroke || textTarget.get?.('stroke') || textElement.properties.strokeColor,
   backgroundColor: textTarget.backgroundColor || textTarget.get?.('backgroundColor') || textElement.properties.backgroundColor,
   ```

3. **自定义类问题**
   - 问题：`TextboxWithPadding` 等自定义类需要适配新版本
   - 解决：添加 `get()` 方法以兼容新的属性访问方式

### 后续工作

1. 全面检查并修复所有 fabric 相关代码，确保兼容性
2. 更新项目文档，反映新的 API 使用方式
3. 考虑编写单元测试，确保升级后功能正常

### 参考资料

- [Fabric.js 6.7.0 官方文档](http://fabricjs.com/)
- [Fabric.js GitHub 仓库](https://github.com/fabricjs/fabric.js)
- [Fabric.js 6.x 迁移指南](http://fabricjs.com/v6-breaking-changes)
