import { Control, FabricObject, util } from "fabric";
import {
  ROTATE_SVG,
  ROTATE_SVG_ACTIVE,
  ROTATE_CURSOR,
  COPY_SVG,
  DEL_SVG,
  COPY_SVG_ACTIVE,
  DEL_SVG_ACTIVE,
} from "../../../assets/icon";

import { initRectControl } from "./rect";
import { initLineControl } from "./fline";
import { initFTextControl } from "./ftext";

// Create a type for control configurations
type ControlConfig = {
  x: number;
  y: number;
  offsetX?: number;
  offsetY?: number;
  render?: Function;
  cursorStyle?: string;
  mouseUpHandler?: Function;
};

// Consolidate SVG image creation
const createSvgImage = (src: string): HTMLImageElement => {
  const img = document.createElement("img");
  img.src = src;
  return img;
};

const ROTATE_IMG = createSvgImage(ROTATE_SVG);
const ROTATE_IMG_ACTIVE = createSvgImage(ROTATE_SVG_ACTIVE);
const COPY_IMG = createSvgImage(COPY_SVG);
const COPY_IMG_ACTIVE = createSvgImage(COPY_SVG_ACTIVE);
const DEL_IMG = createSvgImage(DEL_SVG);
const DEL_IMG_ACTIVE = createSvgImage(DEL_SVG_ACTIVE);
let _clipboard;
export const FABRITOR_CUSTOM_PROPS = [
  "id",
  "fabritor_desc",
  "selectable",
  "hasControls",
  "sub_type",
  "imageSource",
  "imageBorder",
  "oldArrowInfo",
];

export const removeObject = (target, canvas) => {
  if (!target) {
    target = canvas.getActiveObject();
  }
  if (!target) return;
  if (target.type === "activeSelection") {
    target.getObjects().forEach((obj) => {
      canvas.remove(obj);
    });
    canvas.discardActiveObject();
  } else {
    canvas.remove(target);
  }
  handleMouseOutCorner(target);
  canvas.requestRenderAll();
  canvas.fire("fabritor:del", { target: null });
  return true;
};

const getType = (type) => {
  if (type.indexOf("text") === 0) {
    return "text";
  }
  if (type.indexOf("image/") === 0) {
    return "image";
  }
  return "";
};

const handleFLinePointsWhenMoving = (opt) => {
  const { target, transform, action } = opt;
  if (action === "line-points-change") return;
  const { original } = transform;
  const deltaLeft = target.left - original.left;
  const deltaTop = target.top - original.top;
  target.set({
    x1: target.x1 + deltaLeft,
    y1: target.y1 + deltaTop,
    x2: target.x2 + deltaLeft,
    y2: target.y2 + deltaTop,
  });
};

const renderSizeIcon = (
  ctx,
  left,
  top,
  styleOverride,
  fabricObject,
  TBorLR
) => {
  const xSize = TBorLR === "TB" ? 24 : 8;
  const ySize = TBorLR === "TB" ? 8 : 24;

  ctx.save();
  ctx.translate(left, top);
  ctx.rotate(util.degreesToRadians(fabricObject.angle));

  // 创建阴影效果
  ctx.shadowColor = "rgba(0, 0, 0, 0.15)";
  ctx.shadowBlur = 8;
  ctx.shadowOffsetX = 0;
  ctx.shadowOffsetY = 3;

  // 绘制背景 - 使用主题的主色调
  ctx.fillStyle = "#ffffff";
  ctx.strokeStyle = "#4361ee"; // Material-UI 主色调
  ctx.lineWidth = 2;

  ctx.beginPath();
  ctx.roundRect(-xSize / 2, -ySize / 2, xSize, ySize, 4);
  ctx.fill();
  ctx.stroke();

  // 添加内部高光
  ctx.shadowColor = "transparent";
  ctx.strokeStyle = "rgba(255, 255, 255, 0.9)";
  ctx.lineWidth = 1;
  ctx.beginPath();
  ctx.roundRect(-xSize / 2 + 1, -ySize / 2 + 1, xSize - 2, ySize - 2, 3);
  ctx.stroke();

  ctx.restore();
};

const renderLRIcon = (ctx, left, top, styleOverride, fabricObject) => {
  renderSizeIcon(ctx, left, top, styleOverride, fabricObject, "LR");
};

const renderTBIcon = (ctx, left, top, styleOverride, fabricObject) => {
  renderSizeIcon(ctx, left, top, styleOverride, fabricObject, "TB");
};

export const renderVertexIcon = (
  ctx,
  left,
  top,
  styleOverride,
  fabricObject
) => {
  const size = 16;

  ctx.save();
  ctx.translate(left, top);
  ctx.rotate(util.degreesToRadians(fabricObject.angle));

  // 外层阴影
  ctx.shadowColor = "rgba(0, 0, 0, 0.2)";
  ctx.shadowBlur = 8;
  ctx.shadowOffsetX = 0;
  ctx.shadowOffsetY = 3;

  // 主体圆形 - 使用主题的主色调
  ctx.fillStyle = "#ffffff";
  ctx.strokeStyle = "#4361ee"; // Material-UI 主色调
  ctx.lineWidth = 2.5;

  ctx.beginPath();
  ctx.arc(0, 0, size / 2, 0, 2 * Math.PI, false);
  ctx.fill();
  ctx.stroke();

  // 内部高光环
  ctx.shadowColor = "transparent";
  ctx.strokeStyle = "rgba(255, 255, 255, 0.9)";
  ctx.lineWidth = 1;
  ctx.beginPath();
  ctx.arc(0, 0, size / 2 - 2, 0, 2 * Math.PI, false);
  ctx.stroke();

  // 中心点 - 使用主题的主色调
  ctx.fillStyle = "#4361ee";
  ctx.beginPath();
  ctx.arc(0, 0, 2, 0, 2 * Math.PI, false);
  ctx.fill();

  ctx.restore();
};

// 统一的图标按钮渲染函数 - MUI IconButton 风格
const renderIconButton = (icon: HTMLImageElement) => {
  return (
    ctx: CanvasRenderingContext2D,
    left: number,
    top: number,
    styleOverride: any,
    fabricObject: FabricObject
  ) => {
    const size = 48;

    ctx.save();
    ctx.translate(left, top);
    ctx.rotate(util.degreesToRadians(fabricObject.angle));

    // 轻微的阴影 - 符合 Material Design elevation 1
    ctx.shadowColor = "rgba(0, 0, 0, 0.12)";
    ctx.shadowBlur = 4;
    ctx.shadowOffsetX = 0;
    ctx.shadowOffsetY = 2;

    // 背景圆形 - 简洁的白色背景
    ctx.fillStyle = "#ffffff";
    ctx.strokeStyle = "rgba(0, 0, 0, 0.08)"; // 很淡的边框
    ctx.lineWidth = 1;
    ctx.beginPath();
    ctx.arc(0, 0, size / 2, 0, 2 * Math.PI);
    ctx.fill();
    ctx.stroke();

    // 清除阴影
    ctx.shadowColor = "transparent";

    // 绘制图标
    const iconSize = size * 0.5;
    ctx.globalAlpha = 0.87; // Material Design 标准图标透明度
    ctx.drawImage(icon, -iconSize / 2, -iconSize / 2, iconSize, iconSize);

    ctx.restore();
  };
};

// 统一的激活状态图标按钮渲染函数 - MUI IconButton hover 风格
const renderIconButtonActive = (icon: HTMLImageElement) => {
  return (
    ctx: CanvasRenderingContext2D,
    left: number,
    top: number,
    styleOverride: any,
    fabricObject: FabricObject
  ) => {
    const size = 50;

    ctx.save();
    ctx.translate(left, top);
    ctx.rotate(util.degreesToRadians(fabricObject.angle));

    // Material Design elevation 2 阴影
    ctx.shadowColor = "rgba(0, 0, 0, 0.16)";
    ctx.shadowBlur = 8;
    ctx.shadowOffsetX = 0;
    ctx.shadowOffsetY = 4;

    // 悬停状态背景 - 使用 action.hover 颜色
    ctx.fillStyle = "#ffffff";
    ctx.strokeStyle = "rgba(67, 97, 238, 0.12)"; // 主题色的淡边框
    ctx.lineWidth = 1;
    ctx.beginPath();
    ctx.arc(0, 0, size / 2, 0, 2 * Math.PI);
    ctx.fill();
    ctx.stroke();

    // 添加悬停时的背景色调
    ctx.shadowColor = "transparent";
    ctx.fillStyle = "rgba(67, 97, 238, 0.04)"; // 主题色的悬停背景
    ctx.beginPath();
    ctx.arc(0, 0, size / 2 - 1, 0, 2 * Math.PI);
    ctx.fill();

    // 绘制激活状态的图标 - 更高的透明度
    const iconSize = size * 0.5;
    ctx.globalAlpha = 1; // 激活时完全不透明
    ctx.drawImage(icon, -iconSize / 2, -iconSize / 2, iconSize, iconSize);

    ctx.restore();
  };
};

// Simplified render functions (保持向后兼容)
const renderSvgIcon = (icon: HTMLImageElement) => {
  return renderIconButton(icon);
};

function renderSvgIconActive(icon) {
  return renderIconButtonActive(icon);
}

const handleCopyObject = async (eventData, transform) => {
  const target = transform.target;
  const canvas = target.canvas;
  const editorElement = canvas.store.getActiveElement();
  if (editorElement) {
    canvas.store.cloneElement(editorElement.id);
  }
  return true;
};

const handleDelObject = (eventData, transform) => {
  const target = transform.target;
  const canvas = target.canvas;
  const store = canvas.store;

  // 检查是否是字幕对象
  if (
    store.captionManager &&
    target === store.captionManager.captionTextObject
  ) {
    // 如果是字幕对象，删除当前选中的字幕
    const selectedCaption = store.getSelectedCaption();
    if (selectedCaption) {
      store.deleteCaption(selectedCaption.id);
    }
  } else {
    // 如果是普通元素，使用原有逻辑
    const editorElement = store.getActiveElement();
    if (editorElement) {
      store.deleteElement(editorElement.id);
    }
  }
  return true;
};

// Consolidated control configuration application
const applyControlConfig = (controlName: string, config: ControlConfig) => {
  Object.keys(config).forEach((key) => {
    FabricObject.prototype.controls[controlName][key] = config[key];
  });
};

export const renderController = () => {
  const controlConfigs: Record<string, ControlConfig> = {
    mt: { x: 0, y: -0.5, offsetY: -1, render: renderTBIcon },
    mb: { x: 0, y: 0.5, offsetY: 1, render: renderTBIcon },
    ml: { x: -0.5, y: 0, offsetX: -1, render: renderLRIcon },
    mr: { x: 0.5, y: 0, offsetX: 1, render: renderLRIcon },
    tl: { x: -0.5, y: -0.5, render: renderVertexIcon },
    tr: { x: 0.5, y: -0.5, render: renderVertexIcon },
    bl: { x: -0.5, y: 0.5, render: renderVertexIcon },
    br: { x: 0.5, y: 0.5, render: renderVertexIcon },
  };

  Object.entries(controlConfigs).forEach(([name, config]) => {
    applyControlConfig(name, config);
  });
};

// reference: https://medium.com/@luizzappa/custom-icon-and-cursor-in-fabric-js-controls-4714ba0ac28f
export const renderRotateController = () => {
  const mtrConfig = {
    x: 0,
    y: 0.5,
    offsetY: 42, // 调整偏移量以适应统一的按钮大小
    cursorStyle: "pointer", // 统一cursor样式
    cursorStyleHandler: () =>
      `url("data:image/svg+xml;charset=utf-8,${ROTATE_CURSOR}") 12 12, crosshair`,
    render: renderIconButton(ROTATE_IMG),
    withConnection: false,
  };
  Object.keys(mtrConfig).forEach((key) => {
    FabricObject.prototype.controls.mtr[key] = mtrConfig[key];
  });
};

// copy & paste & delete & more
export const renderToolBarController = () => {
  const copyControl = new Control({
    x: 0,
    y: -0.5,
    offsetX: -30, // 调整间距以适应统一的按钮大小
    offsetY: -42, // 与旋转按钮保持相同的偏移距离
    cursorStyle: "pointer",
    mouseUpHandler: handleCopyObject as any,
    render: renderIconButton(COPY_IMG),
    withConnection: false, // 与旋转按钮保持一致
  });
  FabricObject.prototype.controls.copy = copyControl;

  const delControl = new Control({
    x: 0,
    y: -0.5,
    offsetX: 30, // 调整间距以适应统一的按钮大小
    offsetY: -42, // 与旋转按钮保持相同的偏移距离
    cursorStyle: "pointer",
    mouseUpHandler: handleDelObject,
    render: renderIconButton(DEL_IMG),
    withConnection: false, // 与旋转按钮保持一致
  });
  FabricObject.prototype.controls.del = delControl;
};

export const handleMouseOverCorner = (corner, target) => {
  if (corner === "mtr") {
    target.controls[corner].render = renderIconButtonActive(ROTATE_IMG_ACTIVE);
  }
  if (corner === "copy") {
    target.controls[corner].render = renderIconButtonActive(COPY_IMG_ACTIVE);
  }
  if (corner === "del") {
    target.controls[corner].render = renderIconButtonActive(DEL_IMG_ACTIVE);
  }
  target.canvas.requestRenderAll();
};

export const handleMouseOutCorner = (target) => {
  if (!target) return;
  if (target.controls?.mtr) {
    target.controls.mtr.render = renderIconButton(ROTATE_IMG);
  }
  if (target.controls?.copy) {
    target.controls.copy.render = renderIconButton(COPY_IMG);
  }
  if (target.controls?.del) {
    target.controls.del.render = renderIconButton(DEL_IMG);
  }
};

export default function initControl() {
  renderController();
  renderRotateController();
  renderToolBarController();
  initRectControl();
  initLineControl();
  initFTextControl();
}
