import { Point, controlsUtils, FabricObject, Textbox, Control } from "fabric";

/**
 * Transforms a point described by x and y in a distance from the top left corner of the object
 * bounding box.
 * @param {Object} transform
 * @param {String} originX
 * @param {String} originY
 * @param {number} x
 * @param {number} y
 * @return {Fabric.Point} the normalized point
 */
export const getLocalPoint = (transform, originX, originY, x, y) => {
  var target = transform.target,
    control = target.controls[transform.corner],
    zoom = target.canvas.getZoom(),
    padding = target.padding / zoom,
    localPoint = target.toLocalPoint(new Point(x, y), originX, originY);
  if (localPoint.x >= padding) {
    localPoint.x -= padding;
  }
  if (localPoint.x <= -padding) {
    localPoint.x += padding;
  }
  if (localPoint.y >= padding) {
    localPoint.y -= padding;
  }
  if (localPoint.y <= padding) {
    localPoint.y += padding;
  }
  localPoint.x -= control.offsetX;
  localPoint.y -= control.offsetY;
  return localPoint;
};

function isTransformCentered(transform) {
  return transform.originX === "center" && transform.originY === "center";
}

const _changeHeight = (eventData, transform, x, y) => {
  const target = transform.target,
    localPoint = getLocalPoint(
      transform,
      transform.originX,
      transform.originY,
      x,
      y
    ),
    strokePadding =
      target.strokeWidth / (target.strokeUniform ? target.scaleX : 1),
    multiplier = isTransformCentered(transform) ? 2 : 1,
    oldHeight = target.height,
    newHeight =
      Math.abs((localPoint.y * multiplier) / target.scaleY) - strokePadding;
  target.set("height", Math.max(newHeight, 0));
  return oldHeight !== newHeight;
};
export const initFTextControl = () => {
  const objectControls = FabricObject.prototype.controls;

  if (Textbox) {
    const ftextControl: any = (Textbox.prototype.controls = {});
    // 基础控制点
    ftextControl.tr = objectControls.tr;
    ftextControl.br = objectControls.br;
    ftextControl.tl = objectControls.tl;
    ftextControl.bl = objectControls.bl;
    ftextControl.mt = objectControls.mt;
    ftextControl.mb = objectControls.mb;
    ftextControl.mtr = objectControls.mtr;
    // 确保包含工具栏控制点
    ftextControl.copy = objectControls.copy;
    ftextControl.del = objectControls.del;

    ftextControl.ml = new Control({
      x: -0.5,
      y: 0,
      offsetX: -1,
      actionHandler: controlsUtils.changeWidth as any,
      cursorStyleHandler: objectControls.ml.cursorStyleHandler,
      actionName: "resizing",
      render: objectControls.ml.render,
    });

    ftextControl.mr = new Control({
      x: 0.5,
      y: 0,
      offsetX: 1,
      actionHandler: controlsUtils.changeWidth as any,
      cursorStyleHandler: objectControls.mr.cursorStyleHandler,
      actionName: "resizing",
      render: objectControls.mr.render,
    });
  }
};
