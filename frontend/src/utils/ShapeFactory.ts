import {
  Rect,
  Circle,
  Ellipse,
  Triangle,
  Line,
  Polygon,
  Group,
  Path,
  FabricObject,
} from "fabric";
import { ShapeType, ShapeEditorElement } from "../types";

// 形状创建相关常量
export const SHAPE_CONSTANTS = {
  DEFAULT_BORDER_RADIUS: 10,
  DEFAULT_SIZE: 200,
  DEFAULT_LINE_WIDTH: 300,
  DEFAULT_LINE_HEIGHT: 5,
  DEFAULT_ELLIPSE_WIDTH: 250,
  DEFAULT_ELLIPSE_HEIGHT: 150,
};

// 形状颜色映射，与界面中的颜色保持一致
export const SHAPE_COLOR_MAP: Record<ShapeType, string> = {
  rect: "#F44336", // 红色
  roundedRect: "#2196F3", // 蓝色
  circle: "#FFC107", // 黄色
  ellipse: "#4CAF50", // 绿色
  triangle: "#9C27B0", // 紫色
  line: "#FF9800", // 橙色
  diamond: "#2196F3", // 蓝色
  pentagon: "#F44336", // 红色
  hexagon: "#2196F3", // 蓝色
  octagon: "#FFC107", // 黄色
  rightArrow: "#2196F3", // 蓝色
  upArrow: "#F44336", // 红色
  cross: "#FFC107", // 黄色
  downArrow: "#9C27B0", // 紫色
  polygon: "#9C27B0", // 紫色
  arch: "#673AB7", // 深紫色
  parallelogram: "#009688", // 蓝绿色
  wave: "#2196F3", // 蓝色
  star: "#FFC107", // 黄色
  fourPointStar: "#2196F3", // 蓝色
  sixPointStar: "#F44336", // 红色
  eightPointStar: "#FF9800", // 橙色
  sunBurst: "#FFC107", // 黄色
  semicircle: "#9C27B0", // 紫色
  quarterCircle: "#2196F3", // 蓝色
  ring: "#4CAF50", // 绿色
  halfRing: "#FF9800", // 橙色
  plus: "#2196F3", // 蓝色
};

/**
 * 形状创建选项接口
 */
export interface ShapeCreationOptions {
  fill?: string;
  stroke?: string;
  strokeWidth?: number;
  borderRadius?: number;
  id?: string;
}

/**
 * 形状尺寸接口
 */
export interface ShapeDimensions {
  width: number;
  height: number;
}

/**
 * 形状属性接口
 */
export interface ShapeProperties {
  shapeType: ShapeType;
  fill: string;
  stroke: string;
  strokeWidth: number;
  borderRadius: number;
  border: {
    width: number;
    color: string;
    style: string;
    borderRadius: number;
  };
}

/**
 * 形状工厂类 - 负责创建各种类型的形状
 */
export class ShapeFactory {
  /**
   * 获取指定形状类型的默认尺寸
   */
  static getShapeDimensions(shapeType: ShapeType): ShapeDimensions {
    const defaultSize = SHAPE_CONSTANTS.DEFAULT_SIZE;

    switch (shapeType) {
      case "rect":
        // 矩形使用40:30的宽高比
        return {
          width: defaultSize,
          height: Math.round(defaultSize * 0.75), // 保持与界面预览相同的40:30比例
        };
      case "roundedRect":
        // 圆角矩形使用正方形
        return {
          width: defaultSize,
          height: defaultSize,
        };
      case "line":
        return {
          width: SHAPE_CONSTANTS.DEFAULT_LINE_WIDTH,
          height: SHAPE_CONSTANTS.DEFAULT_LINE_HEIGHT,
        };
      case "ellipse":
      case "parallelogram":
        return {
          width: SHAPE_CONSTANTS.DEFAULT_ELLIPSE_WIDTH,
          height: SHAPE_CONSTANTS.DEFAULT_ELLIPSE_HEIGHT,
        };
      default:
        return { width: defaultSize, height: defaultSize };
    }
  }

  /**
   * 创建形状属性对象
   */
  static createShapeProperties(
    shapeType: ShapeType,
    options?: ShapeCreationOptions
  ): ShapeProperties {
    const defaultColor = SHAPE_COLOR_MAP[shapeType] || "#9e9e9e";

    return {
      shapeType,
      fill: options?.fill || defaultColor,
      stroke: options?.stroke || "#757575",
      strokeWidth: options?.strokeWidth || 1,
      borderRadius:
        options?.borderRadius ||
        (shapeType === "roundedRect"
          ? SHAPE_CONSTANTS.DEFAULT_BORDER_RADIUS
          : 0),
      border: {
        width: 0,
        color: "transparent",
        style: "solid",
        borderRadius: 0,
      },
    };
  }

  /**
   * 创建fabric.js形状对象
   */
  static createShapeObject(
    shapeType: ShapeType,
    commonProps: any,
    properties: ShapeEditorElement["properties"]
  ): FabricObject {
    const { width, height } = commonProps;

    const shapeCreators = {
      rect: () =>
        new Rect({
          ...commonProps,
          rx: 1, // 与界面中的borderRadius: 1保持一致
          ry: 1, // 与界面中的borderRadius: 1保持一致
        }),
      roundedRect: () =>
        new Rect({
          ...commonProps,
          // 移除rx和ry，使其成为无圆角的正方形
        }),
      circle: () =>
        new Circle({
          ...commonProps,
          radius: Math.min(width, height) / 2,
        }),
      ellipse: () =>
        new Ellipse({
          ...commonProps,
          rx: width / 2,
          ry: height / 2,
        }),
      triangle: () => new Triangle(commonProps),
      line: () =>
        new Line([0, 0, width, 0], {
          ...commonProps,
          stroke: properties.stroke,
          strokeWidth: properties.strokeWidth || 5,
        }),
      polygon: () => this.createPolygonShape(5, commonProps),
      pentagon: () => this.createPolygonShape(5, commonProps),
      hexagon: () => this.createPolygonShape(6, commonProps),
      octagon: () => this.createPolygonShape(8, commonProps),
      parallelogram: () => this.createParallelogram(commonProps),
      arch: () => this.createArch(commonProps),
      diamond: () => this.createDiamond(commonProps),
      rightArrow: () => this.createRightArrow(commonProps),
      upArrow: () => this.createUpArrow(commonProps),
      downArrow: () => this.createDownArrow(commonProps),
      cross: () => this.createCross(commonProps),
      wave: () => this.createWave(commonProps),
      star: () => this.createStar(commonProps),
      fourPointStar: () => this.createFourPointStar(commonProps),
      sixPointStar: () => this.createSixPointStar(commonProps),
      eightPointStar: () => this.createEightPointStar(commonProps),
      sunBurst: () => this.createSunBurst(commonProps),
      semicircle: () => this.createSemicircle(commonProps),
      quarterCircle: () => this.createQuarterCircle(commonProps),
      ring: () => this.createRing(commonProps),
      halfRing: () => this.createHalfRing(commonProps),
      plus: () => this.createPlus(commonProps),
    };

    const creator = shapeCreators[shapeType] || shapeCreators.rect;
    return creator();
  }

  /**
   * 计算正多边形的顶点坐标
   */
  static calculatePolygonPoints(sides: number, radius: number) {
    const points = [];
    const angleStep = (2 * Math.PI) / sides;

    for (let i = 0; i < sides; i++) {
      const angle = i * angleStep - Math.PI / 2; // 从顶部开始
      points.push({
        x: radius + radius * Math.cos(angle),
        y: radius + radius * Math.sin(angle),
      });
    }

    return points;
  }

  // ===== 基础形状创建方法 =====

  private static createPolygonShape(sides: number, commonProps: any): Polygon {
    return new Polygon(
      this.calculatePolygonPoints(sides, commonProps.width / 2),
      commonProps
    );
  }

  private static createParallelogram(commonProps: any): Polygon {
    const { width, height } = commonProps;
    // 修改点坐标以匹配界面中显示的平行四边形
    // 界面中使用的是 clipPath: "polygon(25% 0%, 100% 0%, 75% 100%, 0% 100%)"
    return new Polygon(
      [
        { x: width * 0.25, y: 0 },
        { x: width, y: 0 },
        { x: width * 0.75, y: height },
        { x: 0, y: height },
      ],
      commonProps
    );
  }

  private static createArch(commonProps: any): Path {
    const { width, height } = commonProps;
    // 修改路径以匹配界面中显示的拱形
    // 界面中使用的是带有圆角的矩形顶部，底部是直线
    // 创建一个路径，从左下角开始，到左上角，然后是圆弧到右上角，再到右下角，最后回到起点
    const radius = width / 2; // 圆弧半径
    const archPath = `M 0 ${height} L 0 ${
      height / 2
    } A ${radius} ${radius} 0 0 1 ${width} ${
      height / 2
    } L ${width} ${height} Z`;
    return new Path(archPath, commonProps);
  }

  private static createDiamond(commonProps: any): Polygon {
    const { width, height } = commonProps;
    return new Polygon(
      [
        { x: width / 2, y: 0 },
        { x: width, y: height / 2 },
        { x: width / 2, y: height },
        { x: 0, y: height / 2 },
      ],
      commonProps
    );
  }

  // ===== 箭头形状创建方法 =====

  private static createRightArrow(commonProps: any): Path {
    const { width, height } = commonProps;
    const arrowPath = `M 0 ${height / 3} L ${width * 0.6} ${height / 3} L ${
      width * 0.6
    } 0 L ${width} ${height / 2} L ${width * 0.6} ${height} L ${width * 0.6} ${
      (height * 2) / 3
    } L 0 ${(height * 2) / 3} Z`;
    return new Path(arrowPath, commonProps);
  }

  private static createUpArrow(commonProps: any): Path {
    const { width, height } = commonProps;
    const arrowPath = `M ${width / 3} ${height} L ${width / 3} ${
      height * 0.4
    } L 0 ${height * 0.4} L ${width / 2} 0 L ${width} ${height * 0.4} L ${
      (width * 2) / 3
    } ${height * 0.4} L ${(width * 2) / 3} ${height} Z`;
    return new Path(arrowPath, commonProps);
  }

  private static createDownArrow(commonProps: any): Path {
    const { width, height } = commonProps;
    const arrowPath = `M ${width / 3} 0 L ${width / 3} ${height * 0.6} L 0 ${
      height * 0.6
    } L ${width / 2} ${height} L ${width} ${height * 0.6} L ${
      (width * 2) / 3
    } ${height * 0.6} L ${(width * 2) / 3} 0 Z`;
    return new Path(arrowPath, commonProps);
  }

  // ===== 线条和图标形状创建方法 =====

  private static createCross(commonProps: any): Group {
    const { width, height } = commonProps;
    const centerX = width / 2;
    const centerY = height / 2;
    const size = Math.min(width, height) * 0.6;

    const line1 = new Line(
      [
        centerX - size / 2,
        centerY - size / 2,
        centerX + size / 2,
        centerY + size / 2,
      ],
      {
        stroke: commonProps.fill,
        strokeWidth: Math.max(commonProps.strokeWidth || 1, 6),
        strokeLineCap: "round",
      }
    );

    const line2 = new Line(
      [
        centerX + size / 2,
        centerY - size / 2,
        centerX - size / 2,
        centerY + size / 2,
      ],
      {
        stroke: commonProps.fill,
        strokeWidth: Math.max(commonProps.strokeWidth || 1, 6),
        strokeLineCap: "round",
      }
    );

    return new Group([line1, line2], {
      ...commonProps,
      left: commonProps.left,
      top: commonProps.top,
    });
  }

  private static createWave(commonProps: any): Path {
    const { width, height } = commonProps;
    const wavePath = `M 0 ${height / 2} Q ${width * 0.25} ${height * 0.2} ${
      width * 0.5
    } ${height / 2} T ${width} ${height / 2}`;
    return new Path(wavePath, {
      ...commonProps,
      fill: "",
      stroke: commonProps.fill,
      strokeWidth: Math.max(commonProps.strokeWidth || 1, 6),
      strokeLineCap: "round",
    });
  }

  private static createPlus(commonProps: any): Group {
    const { width, height } = commonProps;
    const centerX = width / 2;
    const centerY = height / 2;
    const thickness = Math.min(width, height) / 6;
    const armLength = Math.min(width, height) * 0.8;

    const horizontal = new Rect({
      width: armLength,
      height: thickness,
      left: centerX,
      top: centerY,
      originX: "center",
      originY: "center",
      fill: commonProps.fill,
      stroke: commonProps.stroke,
      strokeWidth: commonProps.strokeWidth,
    });

    const vertical = new Rect({
      width: thickness,
      height: armLength,
      left: centerX,
      top: centerY,
      originX: "center",
      originY: "center",
      fill: commonProps.fill,
      stroke: commonProps.stroke,
      strokeWidth: commonProps.strokeWidth,
    });

    return new Group([horizontal, vertical], {
      ...commonProps,
      left: commonProps.left,
      top: commonProps.top,
    });
  }

  // ===== 星形创建方法 =====

  private static createStar(commonProps: any): Polygon {
    const { width, height } = commonProps;
    const centerX = width / 2;
    const centerY = height / 2;
    const outerRadius = (Math.min(width, height) / 2) * 0.9;
    const innerRadius = outerRadius * 0.4;
    const points = [];

    for (let i = 0; i < 10; i++) {
      const angle = (i * Math.PI) / 5;
      const radius = i % 2 === 0 ? outerRadius : innerRadius;
      const x = centerX + Math.cos(angle - Math.PI / 2) * radius;
      const y = centerY + Math.sin(angle - Math.PI / 2) * radius;
      points.push({ x, y });
    }

    return new Polygon(points, commonProps);
  }

  private static createFourPointStar(commonProps: any): Polygon {
    const { width, height } = commonProps;
    const centerX = width / 2;
    const centerY = height / 2;
    const outerRadius = (Math.min(width, height) / 2) * 0.9;
    const innerRadius = outerRadius * 0.3;
    const points = [];

    for (let i = 0; i < 8; i++) {
      const angle = (i * Math.PI) / 4;
      const radius = i % 2 === 0 ? outerRadius : innerRadius;
      const x = centerX + Math.cos(angle) * radius;
      const y = centerY + Math.sin(angle) * radius;
      points.push({ x, y });
    }

    return new Polygon(points, commonProps);
  }

  private static createSixPointStar(commonProps: any): Polygon {
    const { width, height } = commonProps;
    const centerX = width / 2;
    const centerY = height / 2;
    const outerRadius = (Math.min(width, height) / 2) * 0.9;
    const innerRadius = outerRadius * 0.4;
    const points = [];

    for (let i = 0; i < 12; i++) {
      const angle = (i * Math.PI) / 6;
      const radius = i % 2 === 0 ? outerRadius : innerRadius;
      const x = centerX + Math.cos(angle - Math.PI / 2) * radius;
      const y = centerY + Math.sin(angle - Math.PI / 2) * radius;
      points.push({ x, y });
    }

    return new Polygon(points, commonProps);
  }

  private static createEightPointStar(commonProps: any): Polygon {
    const { width, height } = commonProps;
    const centerX = width / 2;
    const centerY = height / 2;
    const outerRadius = (Math.min(width, height) / 2) * 0.9;
    const innerRadius = outerRadius * 0.4;
    const points = [];

    for (let i = 0; i < 16; i++) {
      const angle = (i * Math.PI) / 8;
      const radius = i % 2 === 0 ? outerRadius : innerRadius;
      const x = centerX + Math.cos(angle - Math.PI / 2) * radius;
      const y = centerY + Math.sin(angle - Math.PI / 2) * radius;
      points.push({ x, y });
    }

    return new Polygon(points, commonProps);
  }

  private static createSunBurst(commonProps: any): Group {
    const { width, height } = commonProps;
    const centerX = width / 2;
    const centerY = height / 2;
    const innerRadius = Math.min(width, height) / 4;
    const outerRadius = (Math.min(width, height) / 2) * 0.9;

    // 创建中心圆
    const center = new Circle({
      radius: innerRadius,
      left: centerX,
      top: centerY,
      originX: "center",
      originY: "center",
      fill: commonProps.fill,
      stroke: commonProps.stroke,
      strokeWidth: commonProps.strokeWidth,
    });

    // 创建射线
    const rays = [];
    for (let i = 0; i < 16; i++) {
      const angle = (i * Math.PI) / 8;
      const x1 = centerX + Math.cos(angle) * innerRadius;
      const y1 = centerY + Math.sin(angle) * innerRadius;
      const x2 = centerX + Math.cos(angle) * outerRadius;
      const y2 = centerY + Math.sin(angle) * outerRadius;

      const ray = new Line([x1, y1, x2, y2], {
        stroke: commonProps.fill,
        strokeWidth: Math.max(commonProps.strokeWidth || 1, 3),
        strokeLineCap: "round",
      });
      rays.push(ray);
    }

    return new Group([center, ...rays], {
      ...commonProps,
      left: commonProps.left,
      top: commonProps.top,
    });
  }

  // ===== 圆弧和环形创建方法 =====

  private static createSemicircle(commonProps: any): Path {
    const { width, height } = commonProps;
    const radius = width / 2;
    const centerX = width / 2;
    const centerY = height;

    const semicirclePath = `M 0 ${centerY} A ${radius} ${radius} 0 0 1 ${width} ${centerY} Z`;

    return new Path(semicirclePath, commonProps);
  }

  private static createQuarterCircle(commonProps: any): Path {
    const { width, height } = commonProps;
    const radius = Math.min(width, height);

    // 创建左下角四分之一圆（与界面图标一致）
    // 这是一个纯粹的四分之一圆，占据左下角区域
    // SVG路径：从左上角到左下角，然后弧线到右上角，再直线回到起点
    const quarterCirclePath = `M 0 0 L 0 ${radius} A ${radius} ${radius} 0 0 0 ${radius} 0 Z`;

    return new Path(quarterCirclePath, commonProps);
  }

  private static createRing(commonProps: any): Path {
    const { width, height } = commonProps;
    const centerX = width / 2;
    const centerY = height / 2;
    const outerRadius = (Math.min(width, height) / 2) * 0.9;
    const innerRadius = outerRadius * 0.5;

    // 使用SVG路径创建环形：外圆减去内圆
    // 外圆：顺时针方向，内圆：逆时针方向（这样内圆会被"挖空"）
    const ringPath = `
      M ${centerX + outerRadius} ${centerY}
      A ${outerRadius} ${outerRadius} 0 1 1 ${centerX - outerRadius} ${centerY}
      A ${outerRadius} ${outerRadius} 0 1 1 ${centerX + outerRadius} ${centerY}
      M ${centerX + innerRadius} ${centerY}
      A ${innerRadius} ${innerRadius} 0 1 0 ${centerX - innerRadius} ${centerY}
      A ${innerRadius} ${innerRadius} 0 1 0 ${centerX + innerRadius} ${centerY}
      Z
    `;

    return new Path(ringPath, {
      ...commonProps,
      fillRule: "evenodd", // 确保内圆被正确"挖空"
    });
  }

  private static createHalfRing(commonProps: any): Path {
    const { width, height } = commonProps;
    const centerX = width / 2;
    const centerY = height;
    const outerRadius = width / 2;
    const innerRadius = outerRadius * 0.6;

    const halfRingPath = `M 0 ${centerY} A ${outerRadius} ${outerRadius} 0 0 1 ${width} ${centerY} L ${
      width - (outerRadius - innerRadius)
    } ${centerY} A ${innerRadius} ${innerRadius} 0 0 0 ${
      outerRadius - innerRadius
    } ${centerY} Z`;

    return new Path(halfRingPath, commonProps);
  }
}
